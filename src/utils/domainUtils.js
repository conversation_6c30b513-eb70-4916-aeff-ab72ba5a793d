import { MAIN_DOMAIN, FT_DOMAIN } from "./consts";

export const isOnFastTrackDomain = () => {
  return window.location.hostname === FT_DOMAIN;
};

export const redirectToMainPortal = () => {
  const currentPath = window.location.pathname;
  const currentSearch = window.location.search;
  const mainUrl = `https://${MAIN_DOMAIN}${currentPath}${currentSearch}`;
  window.location.href = mainUrl;
};

export const redirectToFastTrack = () => {
  const currentPath = window.location.pathname;
  const currentSearch = window.location.search;
  const ftUrl = `https://${FT_DOMAIN}${currentPath}${currentSearch}`;
  window.location.href = ftUrl;
};
